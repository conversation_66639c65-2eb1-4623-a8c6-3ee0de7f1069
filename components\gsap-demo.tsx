"use client";

import { useEffect, useRef } from "react";
import { useGSAP } from "@/hooks/useGSAP";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

export default function GSAPDemo() {
  const { fadeIn, slideIn, staggerAnimation, parallax } = useGSAP();
  const containerRef = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const cardsRef = useRef<HTMLDivElement>(null);
  const backgroundRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Demo animations
    if (titleRef.current) {
      fadeIn(titleRef.current, {
        trigger: containerRef.current || undefined,
        start: "top 80%",
        duration: 1.5,
        from: { opacity: 0, y: 50, scale: 0.9 },
        to: { opacity: 1, y: 0, scale: 1 }
      });
    }

    if (cardsRef.current) {
      staggerAnimation(Array.from(cardsRef.current.children), {
        trigger: cardsRef.current,
        start: "top 85%",
        delay: 0.5,
        stagger: 0.2,
        duration: 1,
        from: { opacity: 0, y: 60, rotationY: 45 },
        to: { opacity: 1, y: 0, rotationY: 0 }
      });
    }

    if (backgroundRef.current) {
      parallax(backgroundRef.current, 0.5, {
        start: "top bottom",
        end: "bottom top"
      });
    }
  }, [fadeIn, slideIn, staggerAnimation, parallax]);

  const demoCards = [
    {
      title: "Fade In Animation",
      description: "Elements smoothly fade in with customizable easing and timing",
      features: ["Opacity transitions", "Scale effects", "Custom delays"]
    },
    {
      title: "Slide Animations",
      description: "Content slides in from any direction with smooth motion",
      features: ["Directional slides", "Smooth easing", "Responsive timing"]
    },
    {
      title: "Stagger Effects",
      description: "Sequential animations for multiple elements",
      features: ["Customizable stagger", "Group animations", "Performance optimized"]
    },
    {
      title: "Parallax Scrolling",
      description: "Background elements move at different speeds for depth",
      features: ["Depth perception", "Smooth scrolling", "Hardware accelerated"]
    }
  ];

  return (
    <section className="py-20 bg-gradient-to-br from-muted/30 to-background relative overflow-hidden">
      {/* Parallax Background */}
      <div
        ref={backgroundRef}
        className="absolute inset-0 bg-gradient-to-br from-primary/5 to-purple-500/5"
      />
      
      <div ref={containerRef} className="container mx-auto px-4 relative z-10">
        <h2
          ref={titleRef}
          className="text-4xl md:text-5xl font-bold text-center mb-16 bg-gradient-to-r from-primary to-purple-600 bg-clip-text text-transparent"
        >
          GSAP Animation Showcase
        </h2>

        <div ref={cardsRef} className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          {demoCards.map((card) => (
            <Card key={card.title} className="hover:shadow-xl transition-shadow duration-300">
              <CardHeader>
                <CardTitle className="text-lg">{card.title}</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground mb-4 text-sm">
                  {card.description}
                </p>
                <div className="space-y-2">
                  {card.features.map((feature) => (
                    <Badge key={feature} variant="outline" className="text-xs mr-2">
                      {feature}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="text-center mt-12">
          <p className="text-muted-foreground">
            Scroll up and down to see the animations in action!
          </p>
        </div>
      </div>
    </section>
  );
}
