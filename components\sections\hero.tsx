"use client";

import { motion } from "framer-motion";
import { ArrowRight, Mail, Github, Linkedin, ArrowDown, Code, Download } from "lucide-react";
import { useRef } from "react";
import { useGSAP } from "@/hooks/useGSAP";
import { useTypewriter } from "@/hooks/useTypewriter";
import { InteractiveBackground } from "@/components/interactive-background";

export default function HeroSection() {
  const { scrollTo } = useGSAP();
  const heroRef = useRef<HTMLDivElement>(null);

  // Auto-typing effect for developer roles
  const { text: typedRole } = useTypewriter({
    words: [
      "Full Stack Developer",
      "Web Developer with AI Skills",
      "Transforming Ideas into Full-Stack Products",
      "Turning Ideas into Code"
    ],
    typeSpeed: 80,
    deleteSpeed: 40,
    delaySpeed: 2500,
  });

  const scrollToSection = (sectionId: string) => {
    scrollTo(`#${sectionId}`, 1.2);
  };

  // Using Framer Motion for animations instead of GSAP

  return (
    <section id="home" className="relative min-h-screen flex items-center justify-center overflow-hidden hero-mobile-padding md:pt-0">
      {/* Interactive Background */}
      <InteractiveBackground />

      {/* Gradient Overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-background/90 via-background/70 to-background/90 z-0"></div>

      <div className="container mx-auto px-4 relative z-10" ref={heroRef}>
        <div className="grid lg:grid-cols-2 gap-8 items-center max-w-6xl mx-auto">

          {/* Left Side - Content */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            className="space-y-6"
          >
            {/* Status Badge */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="inline-flex items-center gap-2 px-4 py-2 bg-green-500/10 border border-green-500/20 rounded-full text-green-600 text-sm font-medium"
            >
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span>Available for opportunities</span>
            </motion.div>

            {/* Main Heading */}
            <div className="space-y-3">
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.3 }}
                className="text-3xl md:text-5xl lg:text-6xl font-bold leading-tight"
              >
                <div className="text-foreground mb-2">Hi, I'm</div>
                <div className="bg-gradient-to-r from-primary via-blue-500 to-purple-600 bg-clip-text text-transparent">
                  Saurabh Dahariya
                </div>
              </motion.div>

              {/* Auto-typing Role */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.5 }}
                className="text-lg md:text-2xl font-semibold text-muted-foreground"
              >
                <span className="text-primary font-mono">&gt; </span>
                <span>{typedRole}</span>
                <span className="animate-pulse text-primary">_</span>
              </motion.div>
            </div>

            {/* Description */}
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.7 }}
              className="text-base md:text-lg text-muted-foreground leading-relaxed max-w-lg"
            >
              Building <span className="text-primary font-semibold">modern web applications</span> with
              React, AI integration, and clean code practices.
            </motion.p>

            {/* Tech Stack */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.9 }}
              className="flex flex-wrap gap-2"
            >
              {[
                { name: 'React', icon: '⚛️' },
                { name: 'Node.js', icon: '🟢' },
                { name: 'TypeScript', icon: '🔷' },
                { name: 'OpenAI', icon: '🤖' }
              ].map((tech, index) => (
                <motion.div
                  key={tech.name}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, delay: 1.1 + index * 0.1 }}
                  className="flex items-center gap-1.5 px-3 py-1.5 bg-muted/50 border border-border/50 rounded-lg text-sm font-medium hover:bg-primary/10 hover:border-primary/20 transition-all cursor-default"
                >
                  <span>{tech.icon}</span>
                  <span>{tech.name}</span>
                </motion.div>
              ))}
            </motion.div>

            {/* Action Buttons */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 1.3 }}
              className="flex flex-wrap gap-3 justify-center lg:justify-start"
            >
              <motion.button
                onClick={() => scrollToSection('projects')}
                whileHover={{ scale: 1.02, y: -1 }}
                whileTap={{ scale: 0.98 }}
                className="group bg-gradient-to-r from-primary to-blue-600 hover:from-primary/90 hover:to-blue-600/90 text-white px-6 py-2.5 rounded-lg font-medium transition-all flex items-center gap-2 shadow-md text-sm"
              >
                <Code className="h-4 w-4" />
                <span>View Work</span>
                <ArrowRight className="h-4 w-4 group-hover:translate-x-0.5 transition-transform" />
              </motion.button>

              <motion.a
                href="/Saurabh-Dahariya-Resume.pdf"
                download="Saurabh-Dahariya-Resume.pdf"
                whileHover={{ scale: 1.02, y: -1 }}
                whileTap={{ scale: 0.98 }}
                className="group bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-600/90 hover:to-pink-600/90 text-white px-6 py-2.5 rounded-lg font-medium transition-all flex items-center gap-2 shadow-md text-sm"
              >
                <Download className="h-4 w-4" />
                <span>Resume</span>
                <ArrowRight className="h-4 w-4 group-hover:translate-x-0.5 transition-transform" />
              </motion.a>

              <motion.button
                onClick={() => scrollToSection('contact')}
                whileHover={{ scale: 1.02, y: -1 }}
                whileTap={{ scale: 0.98 }}
                className="group border border-primary/40 bg-background/80 backdrop-blur-sm hover:bg-primary/10 hover:border-primary/60 px-6 py-2.5 rounded-lg font-medium transition-all flex items-center gap-2 text-sm"
              >
                <Mail className="h-4 w-4 group-hover:scale-105 transition-transform" />
                <span>Contact</span>
              </motion.button>
            </motion.div>

            {/* Social Links */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 1.5 }}
              className="flex gap-3 justify-center lg:justify-start"
            >
              {[
                { icon: Github, href: "https://github.com/saurabhdahariya", label: "GitHub" },
                { icon: Linkedin, href: "https://www.linkedin.com/in/saurabh-dahariya/", label: "LinkedIn" },
                { icon: Mail, href: "mailto:<EMAIL>", label: "Email" }
              ].map((social) => (
                <motion.a
                  key={social.label}
                  href={social.href}
                  target={social.label !== "Email" ? "_blank" : undefined}
                  rel={social.label !== "Email" ? "noopener noreferrer" : undefined}
                  whileHover={{ scale: 1.05, y: -1 }}
                  whileTap={{ scale: 0.95 }}
                  className="p-2.5 bg-muted/50 hover:bg-primary/10 border border-border/50 hover:border-primary/30 rounded-lg transition-all group"
                  title={social.label}
                >
                  <social.icon className="h-4 w-4 text-muted-foreground group-hover:text-primary transition-colors" />
                </motion.a>
              ))}
            </motion.div>
          </motion.div>
          {/* Right Side - Hero Image */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.4, ease: "easeOut" }}
            className="relative flex flex-col items-center justify-center"
          >
            {/* Hero Image Container */}
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 1, delay: 0.6 }}
              className="relative"
            >
              {/* Clean Image Container */}
              <div className="relative">
                <motion.img
                  src="/hero.png"
                  alt="Saurabh Dahariya - Full Stack Developer"
                  className="w-72 h-auto lg:w-96 lg:h-auto object-contain"
                  whileHover={{ scale: 1.02 }}
                  transition={{ duration: 0.3 }}
                />

                {/* Subtle Decorative Elements */}
                <motion.div
                  animate={{
                    rotate: [0, 360],
                    scale: [1, 1.1, 1]
                  }}
                  transition={{
                    duration: 12,
                    repeat: Infinity,
                    ease: "linear"
                  }}
                  className="absolute top-1/4 -left-6 w-8 h-8 bg-gradient-to-br from-primary/10 to-blue-500/10 rounded-full blur-lg"
                />

                <motion.div
                  animate={{
                    rotate: [360, 0],
                    scale: [1, 1.2, 1]
                  }}
                  transition={{
                    duration: 15,
                    repeat: Infinity,
                    ease: "linear"
                  }}
                  className="absolute bottom-1/4 -right-6 w-6 h-6 bg-gradient-to-br from-purple-500/10 to-pink-500/10 rounded-full blur-lg"
                />
              </div>
            </motion.div>
          </motion.div>
        </div>

        {/* Scroll Indicator */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1, delay: 2.0 }}
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
        >
          <motion.div
            animate={{ y: [0, 8, 0] }}
            transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
            className="flex flex-col items-center gap-2 text-muted-foreground"
          >
            <span className="text-sm">Scroll to explore</span>
            <ArrowDown className="h-4 w-4" />
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}